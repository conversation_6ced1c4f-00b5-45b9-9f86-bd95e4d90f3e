import { TerrainWeights } from '@/types/terrain'

interface TerrainControlsProps {
  weights: TerrainWeights
  onWeightChange: (terrain: keyof TerrainWeights, delta: number) => void
  onGenerateNew: () => void
}

export function TerrainControls({ weights, onWeightChange, onGenerateNew }: TerrainControlsProps) {
  return (
    <div className="mb-6">
      <h2 className="text-lg font-semibold mb-3 text-gray-200">Terrain Weights</h2>
      <div className="grid grid-cols-2 gap-3 mb-4">
        {Object.entries(weights).map(([terrain, weight]) => (
          <div key={terrain} className="flex flex-col items-center">
            <label className="text-sm font-medium mb-1 capitalize text-gray-300">
              {terrain}
            </label>
            <input
              type="number"
              value={weight}
              onChange={(e) => onWeightChange(terrain as keyof TerrainWeights, parseInt(e.target.value) - weight)}
              className="w-16 px-2 py-1 text-center border border-gray-600 rounded text-sm bg-gray-700 text-white focus:border-blue-400 focus:outline-none"
              min="-50"
              max="50"
            />
            <div className="flex mt-1 space-x-1">
              <button
                onClick={() => onWeightChange(terrain as keyof TerrainWeights, -1)}
                className="w-6 h-6 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors"
              >
                -
              </button>
              <button
                onClick={() => onWeightChange(terrain as keyof TerrainWeights, 1)}
                className="w-6 h-6 bg-green-600 text-white rounded text-xs hover:bg-green-700 transition-colors"
              >
                +
              </button>
            </div>
          </div>
        ))}
      </div>

      <button
        onClick={onGenerateNew}
        className="w-full px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors text-sm font-medium"
      >
        Generate New Planet
      </button>
    </div>
  )
}
