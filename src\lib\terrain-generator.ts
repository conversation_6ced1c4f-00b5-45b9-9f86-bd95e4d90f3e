import * as THREE from 'three'
import { TerrainWeights, TerrainCoverage, PlanetType } from '@/types/terrain'
import { CONTINENT_SCALE, TERRAIN_SCALE, DETAIL_SCALE } from '@/constants/terrain'

// Terrain Generator Class for reusable planet generation
export class TerrainGenerator {
  private planetType: PlanetType
  private weights: TerrainWeights
  private coverage: TerrainCoverage | null

  constructor(planetType: PlanetType, weights: TerrainWeights, coverage?: TerrainCoverage) {
    this.planetType = planetType
    this.weights = weights
    this.coverage = coverage || null
  }

  getTerrainThresholds() {
    // Use new coverage system if available, otherwise fall back to legacy weights
    if (this.coverage) {
      return this.getThresholdsFromCoverage()
    } else {
      return this.getLegacyThresholds()
    }
  }

  private getThresholdsFromCoverage() {
    if (!this.coverage) throw new Error('Coverage not available')

    // Convert percentages to thresholds (0-1 range)
    const oceanThreshold = this.coverage.oceanCoverage / 100
    const landCoverage = 1 - oceanThreshold

    // Calculate land distribution thresholds
    const plainsThreshold = oceanThreshold + (landCoverage * this.coverage.plainsCoverage / 100)
    const hillsThreshold = plainsThreshold + (landCoverage * this.coverage.hillsCoverage / 100)
    // Mountains take up the remaining space (threshold = 1.0)

    return {
      water: oceanThreshold,
      grass: plainsThreshold,
      hills: hillsThreshold,
      mountains: 1.0
    }
  }

  private getLegacyThresholds() {
    // Legacy weight-based system for backward compatibility
    const base = {
      water: 0.45,    // ~45% water coverage (more realistic)
      grass: 0.65,    // ~20% grasslands/plains
      hills: 0.82,    // ~17% hills/forests
      mountains: 1.0  // ~18% mountains/high terrain
    }

    // Apply weight adjustments (each weight unit = 0.015 threshold shift)
    const weightFactor = 0.015
    const adjusted = {
      water: Math.max(0.1, Math.min(0.9, base.water + this.weights.water * weightFactor)),
      grass: Math.max(0.1, Math.min(0.9, base.grass + this.weights.grass * weightFactor)),
      hills: Math.max(0.1, Math.min(0.9, base.hills + this.weights.hills * weightFactor)),
      mountains: 1.0 // Mountains always go to the end
    }

    // Ensure proper ordering with smaller gaps for smoother transitions
    adjusted.grass = Math.max(adjusted.water + 0.03, adjusted.grass)
    adjusted.hills = Math.max(adjusted.grass + 0.03, adjusted.hills)

    return adjusted
  }

  getColor(value: number): THREE.Color {
    const thresholds = this.getTerrainThresholds()
    return this.getBlendedColor(value, thresholds)
  }

  private getBlendedColor(value: number, thresholds: any): THREE.Color {
    const colors = this.planetType.colors
    const waterColor = new THREE.Color(colors.water)
    const grassColor = new THREE.Color(colors.grass)
    const hillsColor = new THREE.Color(colors.hills)
    const mountainsColor = new THREE.Color(colors.mountains)

    // Define blend zones (how much area around each threshold to blend)
    const blendZone = 0.03 // 3% blend zone around each threshold for smoother transitions

    // Add elevation-based color variation within each terrain type
    const elevationVariation = 0.15 // 15% color variation based on elevation

    // Water to Grass transition
    if (value < thresholds.water + blendZone) {
      if (value < thresholds.water - blendZone) {
        // Deep water gets darker, shallow water gets lighter
        const depthFactor = (thresholds.water - blendZone - value) / (thresholds.water - blendZone)
        const darkWater = waterColor.clone().multiplyScalar(1 - elevationVariation * depthFactor)
        return darkWater
      }
      // Smooth blend between water and grass
      const blendFactor = (value - (thresholds.water - blendZone)) / (2 * blendZone)
      const smoothBlend = this.smoothStep(blendFactor)
      return waterColor.clone().lerp(grassColor, smoothBlend)
    }

    // Grass to Hills transition
    if (value < thresholds.hills + blendZone) {
      if (value < thresholds.grass - blendZone) {
        // Add subtle elevation variation to grass
        const elevationFactor = (value - thresholds.water) / (thresholds.grass - thresholds.water)
        const variedGrass = grassColor.clone().multiplyScalar(1 + elevationVariation * elevationFactor * 0.3)
        return variedGrass
      }
      // Smooth blend between grass and hills
      const blendFactor = (value - (thresholds.grass - blendZone)) / (thresholds.hills - thresholds.grass + 2 * blendZone)
      const smoothBlend = this.smoothStep(blendFactor)
      return grassColor.clone().lerp(hillsColor, smoothBlend)
    }

    // Hills to Mountains transition
    if (value < thresholds.mountains) {
      if (value < thresholds.hills - blendZone) {
        // Add elevation variation to hills
        const elevationFactor = (value - thresholds.grass) / (thresholds.hills - thresholds.grass)
        const variedHills = hillsColor.clone().multiplyScalar(1 + elevationVariation * elevationFactor * 0.2)
        return variedHills
      }
      // Smooth blend between hills and mountains
      const blendFactor = (value - (thresholds.hills - blendZone)) / (thresholds.mountains - thresholds.hills + blendZone)
      const smoothBlend = this.smoothStep(blendFactor)
      return hillsColor.clone().lerp(mountainsColor, smoothBlend)
    }

    // High mountains get lighter (snow caps effect)
    const mountainHeight = (value - thresholds.hills) / (1.0 - thresholds.hills)
    const snowEffect = mountainHeight * elevationVariation * 0.4
    return mountainsColor.clone().multiplyScalar(1 + snowEffect)
  }

  // Smooth step function for better blending curves
  private smoothStep(t: number): number {
    t = Math.max(0, Math.min(1, t))
    return t * t * (3 - 2 * t)
  }

  getHeight(value: number): number {
    const thresholds = this.getTerrainThresholds()
    const multipliers = this.planetType.heightMultipliers
    
    if (value < thresholds.water) {
      // More pronounced ocean depths based on how deep below water threshold
      const depth = (thresholds.water - value) / thresholds.water
      return multipliers.water * (1 + depth * 2) // Deeper areas get more depth
    }
    
    if (value < thresholds.grass) {
      // Coastal plains with slight elevation variation
      const elevation = (value - thresholds.water) / (thresholds.grass - thresholds.water)
      return multipliers.grass * (0.5 + elevation * 0.5)
    }
    
    if (value < thresholds.hills) {
      // Rolling hills with progressive height
      const hillHeight = (value - thresholds.grass) / (thresholds.hills - thresholds.grass)
      return multipliers.hills * (0.3 + hillHeight * 1.2)
    }
    
    // Mountains with dramatic height based on elevation above hills threshold
    const mountainHeight = (value - thresholds.hills) / (1.0 - thresholds.hills)
    return multipliers.mountains * (0.5 + mountainHeight * 2.0) // Higher peaks get much taller
  }

  generateTerrainValue(noise2D: (x: number, y: number) => number, lat: number, lon: number): number {
    // Convert spherical coordinates to 3D Cartesian for seamless noise sampling
    const x = Math.cos(lat) * Math.cos(lon)
    const y = Math.cos(lat) * Math.sin(lon)
    const z = Math.sin(lat)

    // Multi-octave noise for more detailed terrain
    // Continental-scale features (major landmasses vs oceans)
    const continentNoise = noise2D(x * CONTINENT_SCALE, y * CONTINENT_SCALE) * 0.6

    // Regional terrain variation (mountain ranges, valleys, plains)
    const terrainNoise = noise2D(y * TERRAIN_SCALE, z * TERRAIN_SCALE) * 0.25

    // Local detail features (hills, small valleys)
    const detailNoise = noise2D(z * DETAIL_SCALE, x * DETAIL_SCALE) * 0.08

    // Add fine-scale surface texture for more realism
    const surfaceNoise = noise2D(x * DETAIL_SCALE * 2.5, y * DETAIL_SCALE * 2.5) * 0.03

    // Add micro-detail for surface roughness
    const microNoise = noise2D(z * DETAIL_SCALE * 5.0, x * DETAIL_SCALE * 5.0) * 0.015

    // Ridged noise for mountain ridges and valleys
    const ridgeNoise = Math.abs(noise2D(y * TERRAIN_SCALE * 0.7, z * TERRAIN_SCALE * 0.7)) * 0.12

    // Combine layers with continental features dominating
    let combined = continentNoise + terrainNoise + detailNoise + surfaceNoise + microNoise + ridgeNoise

    // Normalize to 0-1 range
    combined = (combined + 1) * 0.5

    // Create more realistic ocean/land distribution
    // Bias toward more ocean coverage (like Earth ~71% water)
    combined = combined * 0.8 + 0.1

    // Add latitude-based climate effects
    // More temperate zones in mid-latitudes, ice caps at poles
    const latitudeEffect = Math.cos(lat * 2.0) * 0.08
    combined += latitudeEffect

    // Add some polar ice cap effect (more water/ice at extreme latitudes)
    const polarEffect = Math.pow(Math.abs(Math.sin(lat)), 3) * -0.12
    combined += polarEffect

    // Add subtle equatorial bulge effect
    const equatorialEffect = Math.pow(Math.cos(lat), 2) * 0.03
    combined += equatorialEffect

    return Math.max(0, Math.min(1, combined))
  }
}
