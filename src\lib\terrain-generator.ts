import * as THREE from 'three'
import { TerrainWeights, TerrainCoverage, PlanetType, TerrainRandomness } from '@/types/terrain'
import { CONTINENT_SCALE, TERRAIN_SCALE, DETAIL_SCALE } from '@/constants/terrain'

// Terrain Generator Class for reusable planet generation
export class TerrainGenerator {
  private planetType: PlanetType
  private weights: TerrainWeights
  private coverage: TerrainCoverage | null
  private randomness: TerrainRandomness

  constructor(planetType: PlanetType, weights: TerrainWeights, coverage?: TerrainCoverage, randomness?: TerrainRandomness) {
    this.planetType = planetType
    this.weights = weights
    this.coverage = coverage || null
    this.randomness = randomness || {
      continentalStructure: 70,
      noiseComplexity: 50,
      featureSize: 60
    }
  }

  getTerrainThresholds() {
    // Use new coverage system if available, otherwise fall back to legacy weights
    if (this.coverage) {
      return this.getThresholdsFromCoverage()
    } else {
      return this.getLegacyThresholds()
    }
  }

  private getThresholdsFromCoverage() {
    if (!this.coverage) throw new Error('Coverage not available')

    // Convert percentages to thresholds (0-1 range)
    const oceanThreshold = this.coverage.oceanCoverage / 100
    const landCoverage = 1 - oceanThreshold

    // Calculate land distribution thresholds
    const plainsThreshold = oceanThreshold + (landCoverage * this.coverage.plainsCoverage / 100)
    const hillsThreshold = plainsThreshold + (landCoverage * this.coverage.hillsCoverage / 100)
    // Mountains take up the remaining space (threshold = 1.0)

    return {
      water: oceanThreshold,
      grass: plainsThreshold,
      hills: hillsThreshold,
      mountains: 1.0
    }
  }

  private getLegacyThresholds() {
    // Legacy weight-based system for backward compatibility
    const base = {
      water: 0.45,    // ~45% water coverage (more realistic)
      grass: 0.65,    // ~20% grasslands/plains
      hills: 0.82,    // ~17% hills/forests
      mountains: 1.0  // ~18% mountains/high terrain
    }

    // Apply weight adjustments (each weight unit = 0.015 threshold shift)
    const weightFactor = 0.015
    const adjusted = {
      water: Math.max(0.1, Math.min(0.9, base.water + this.weights.water * weightFactor)),
      grass: Math.max(0.1, Math.min(0.9, base.grass + this.weights.grass * weightFactor)),
      hills: Math.max(0.1, Math.min(0.9, base.hills + this.weights.hills * weightFactor)),
      mountains: 1.0 // Mountains always go to the end
    }

    // Ensure proper ordering with smaller gaps for smoother transitions
    adjusted.grass = Math.max(adjusted.water + 0.03, adjusted.grass)
    adjusted.hills = Math.max(adjusted.grass + 0.03, adjusted.hills)

    return adjusted
  }

  getColor(value: number): THREE.Color {
    const thresholds = this.getTerrainThresholds()
    return this.getBlendedColor(value, thresholds)
  }

  private getBlendedColor(value: number, thresholds: { water: number; grass: number; hills: number; mountains: number }): THREE.Color {
    const colors = this.planetType.colors
    const waterColor = new THREE.Color(colors.water)
    const grassColor = new THREE.Color(colors.grass)
    const hillsColor = new THREE.Color(colors.hills)
    const mountainsColor = new THREE.Color(colors.mountains)

    // Define blend zones (how much area around each threshold to blend)
    const blendZone = 0.03 // 3% blend zone around each threshold for smoother transitions

    // Add elevation-based color variation within each terrain type
    const elevationVariation = 0.15 // 15% color variation based on elevation

    // Water to Grass transition
    if (value < thresholds.water + blendZone) {
      if (value < thresholds.water - blendZone) {
        // Deep water gets darker, shallow water gets lighter
        const depthFactor = (thresholds.water - blendZone - value) / (thresholds.water - blendZone)
        const darkWater = waterColor.clone().multiplyScalar(1 - elevationVariation * depthFactor)
        return darkWater
      }
      // Smooth blend between water and grass
      const blendFactor = (value - (thresholds.water - blendZone)) / (2 * blendZone)
      const smoothBlend = this.smoothStep(blendFactor)
      return waterColor.clone().lerp(grassColor, smoothBlend)
    }

    // Grass to Hills transition
    if (value < thresholds.hills + blendZone) {
      if (value < thresholds.grass - blendZone) {
        // Add subtle elevation variation to grass
        const elevationFactor = (value - thresholds.water) / (thresholds.grass - thresholds.water)
        const variedGrass = grassColor.clone().multiplyScalar(1 + elevationVariation * elevationFactor * 0.3)
        return variedGrass
      }
      // Smooth blend between grass and hills
      const blendFactor = (value - (thresholds.grass - blendZone)) / (thresholds.hills - thresholds.grass + 2 * blendZone)
      const smoothBlend = this.smoothStep(blendFactor)
      return grassColor.clone().lerp(hillsColor, smoothBlend)
    }

    // Hills to Mountains transition
    if (value < thresholds.mountains) {
      if (value < thresholds.hills - blendZone) {
        // Add elevation variation to hills
        const elevationFactor = (value - thresholds.grass) / (thresholds.hills - thresholds.grass)
        const variedHills = hillsColor.clone().multiplyScalar(1 + elevationVariation * elevationFactor * 0.2)
        return variedHills
      }
      // Smooth blend between hills and mountains
      const blendFactor = (value - (thresholds.hills - blendZone)) / (thresholds.mountains - thresholds.hills + blendZone)
      const smoothBlend = this.smoothStep(blendFactor)
      return hillsColor.clone().lerp(mountainsColor, smoothBlend)
    }

    // High mountains get lighter (snow caps effect)
    const mountainHeight = (value - thresholds.hills) / (1.0 - thresholds.hills)
    const snowEffect = mountainHeight * elevationVariation * 0.4
    return mountainsColor.clone().multiplyScalar(1 + snowEffect)
  }

  // Smooth step function for better blending curves
  private smoothStep(t: number): number {
    t = Math.max(0, Math.min(1, t))
    return t * t * (3 - 2 * t)
  }

  getHeight(value: number): number {
    const thresholds = this.getTerrainThresholds()
    const multipliers = this.planetType.heightMultipliers
    
    if (value < thresholds.water) {
      // More pronounced ocean depths based on how deep below water threshold
      const depth = (thresholds.water - value) / thresholds.water
      return multipliers.water * (1 + depth * 2) // Deeper areas get more depth
    }
    
    if (value < thresholds.grass) {
      // Coastal plains with slight elevation variation
      const elevation = (value - thresholds.water) / (thresholds.grass - thresholds.water)
      return multipliers.grass * (0.5 + elevation * 0.5)
    }
    
    if (value < thresholds.hills) {
      // Rolling hills with progressive height
      const hillHeight = (value - thresholds.grass) / (thresholds.hills - thresholds.grass)
      return multipliers.hills * (0.3 + hillHeight * 1.2)
    }
    
    // Mountains with dramatic height based on elevation above hills threshold
    const mountainHeight = (value - thresholds.hills) / (1.0 - thresholds.hills)
    return multipliers.mountains * (0.5 + mountainHeight * 2.0) // Higher peaks get much taller
  }

  generateTerrainValue(noise2D: (x: number, y: number) => number, lat: number, lon: number): number {
    // Convert spherical coordinates to 3D Cartesian for seamless noise sampling
    const x = Math.cos(lat) * Math.cos(lon)
    const y = Math.cos(lat) * Math.sin(lon)
    const z = Math.sin(lat)

    // Calculate randomness factors (0-1 range)
    const structureFactor = this.randomness.continentalStructure / 100
    const complexityFactor = this.randomness.noiseComplexity / 100
    const sizeFactor = this.randomness.featureSize / 100

    // Adjust noise scales based on feature size setting
    const continentScale = CONTINENT_SCALE * (0.5 + sizeFactor * 1.5)  // 0.75x to 2.25x
    const terrainScale = TERRAIN_SCALE * (0.5 + sizeFactor * 1.5)
    const detailScale = DETAIL_SCALE * (0.5 + sizeFactor * 1.5)

    // Generate different noise patterns based on randomness settings
    let combined = 0

    if (structureFactor > 0) {
      // Structured continental patterns (original approach)
      const continentNoise = noise2D(x * continentScale, y * continentScale) * 0.6 * structureFactor
      const terrainNoise = noise2D(y * terrainScale, z * terrainScale) * 0.25 * structureFactor
      combined += continentNoise + terrainNoise
    }

    if (structureFactor < 1) {
      // Random chaotic patterns (completely randomized)
      const randomFactor = 1 - structureFactor

      // Multiple random noise layers with different orientations and offsets
      // Use random offsets to break axis alignment
      const offset1 = 1000 * Math.sin(lat * 3.7 + lon * 2.3)
      const offset2 = 2000 * Math.cos(lat * 4.1 + lon * 3.7)
      const offset3 = 1500 * Math.sin(lat * 2.9 + lon * 4.3)

      const chaos1 = noise2D((x + offset1) * terrainScale * 1.3, (z + offset2) * terrainScale * 0.8) * 0.4 * randomFactor
      const chaos2 = noise2D((z + offset2) * terrainScale * 1.1, (y + offset3) * terrainScale * 1.2) * 0.3 * randomFactor
      const chaos3 = noise2D((y + offset3) * terrainScale * 0.9, (x + offset1) * terrainScale * 1.4) * 0.2 * randomFactor

      // Add rotational chaos to break polar alignment
      const rotatedX = x * Math.cos(lat * 2.1) - z * Math.sin(lat * 2.1)
      const rotatedZ = x * Math.sin(lat * 2.1) + z * Math.cos(lat * 2.1)
      const rotationalChaos = noise2D(rotatedX * terrainScale * 0.7, rotatedZ * terrainScale * 0.7) * 0.3 * randomFactor

      combined += chaos1 + chaos2 + chaos3 + rotationalChaos
    }

    // Add complexity layers based on complexity setting
    const baseComplexity = 0.3 + complexityFactor * 0.7  // 30% to 100% complexity

    // Local detail features (always present but intensity varies)
    const detailNoise = noise2D(z * detailScale, x * detailScale) * 0.08 * baseComplexity

    // Surface texture (varies with complexity)
    const surfaceNoise = noise2D(x * detailScale * 2.5, y * detailScale * 2.5) * 0.03 * complexityFactor

    // Micro-detail (only at high complexity)
    const microNoise = noise2D(z * detailScale * 5.0, x * detailScale * 5.0) * 0.015 * Math.max(0, complexityFactor - 0.5) * 2

    // Ridged noise for dramatic features (varies with complexity)
    const ridgeNoise = Math.abs(noise2D(y * terrainScale * 0.7, z * terrainScale * 0.7)) * 0.12 * complexityFactor

    // Turbulence for chaotic areas (high complexity only)
    const turbulence = Math.abs(noise2D(x * detailScale * 3, y * detailScale * 3)) * 0.06 * Math.max(0, complexityFactor - 0.7) * 3.33

    // Combine all layers
    combined += detailNoise + surfaceNoise + microNoise + ridgeNoise + turbulence

    // Normalize to 0-1 range
    combined = (combined + 1) * 0.5

    // Create more realistic ocean/land distribution
    combined = combined * 0.8 + 0.1

    // Add water distribution randomization for non-axis-aligned oceans
    if (structureFactor < 0.7) {
      // Break axis alignment with additional water distribution noise
      const waterRandomness = 1 - structureFactor

      // Multiple water distribution layers with different scales and orientations
      const waterNoise1 = noise2D(
        (x * Math.cos(lon * 1.7) - y * Math.sin(lon * 1.7)) * continentScale * 0.6,
        (x * Math.sin(lon * 1.7) + y * Math.cos(lon * 1.7)) * continentScale * 0.6
      ) * 0.15 * waterRandomness

      const waterNoise2 = noise2D(
        (z * Math.cos(lat * 2.3) - x * Math.sin(lat * 2.3)) * continentScale * 0.8,
        (z * Math.sin(lat * 2.3) + x * Math.cos(lat * 2.3)) * continentScale * 0.8
      ) * 0.12 * waterRandomness

      // Spiral water distribution to break linear patterns
      const spiralAngle = lat * 2.1 + lon * 1.3
      const spiralX = x * Math.cos(spiralAngle) - y * Math.sin(spiralAngle)
      const spiralY = x * Math.sin(spiralAngle) + y * Math.cos(spiralAngle)
      const spiralWater = noise2D(spiralX * continentScale * 0.5, spiralY * continentScale * 0.5) * 0.1 * waterRandomness

      combined += waterNoise1 + waterNoise2 + spiralWater
    }

    // Add latitude-based climate effects (reduced for more random worlds)
    const climateStrength = 0.5 + structureFactor * 0.5  // Less climate effect for random worlds

    if (structureFactor > 0.3) {
      // Traditional climate effects for structured worlds
      const latitudeEffect = Math.cos(lat * 2.0) * 0.08 * climateStrength
      combined += latitudeEffect

      // Polar effects (also reduced for random worlds)
      const polarEffect = Math.pow(Math.abs(Math.sin(lat)), 3) * -0.12 * climateStrength
      combined += polarEffect

      // Equatorial bulge (minimal for random worlds)
      const equatorialEffect = Math.pow(Math.cos(lat), 2) * 0.03 * structureFactor
      combined += equatorialEffect
    } else {
      // For highly random worlds, add non-axis-aligned climate variation
      const randomClimate1 = Math.sin(lat * 3.7 + lon * 2.1) * 0.04 * (1 - structureFactor)
      const randomClimate2 = Math.cos(lat * 2.3 + lon * 3.9) * 0.03 * (1 - structureFactor)
      combined += randomClimate1 + randomClimate2
    }

    return Math.max(0, Math.min(1, combined))
  }
}
