import * as THREE from 'three'
import { TerrainWeights, PlanetType } from '@/types/terrain'
import { CONTINENT_SCALE, TERRAIN_SCALE, DETAIL_SCALE } from '@/constants/terrain'

// Terrain Generator Class for reusable planet generation
export class TerrainGenerator {
  private planetType: PlanetType
  private weights: TerrainWeights

  constructor(planetType: PlanetType, weights: TerrainWeights) {
    this.planetType = planetType
    this.weights = weights
  }

  getTerrainThresholds() {
    // Base thresholds for more realistic Earth-like distribution
    const base = {
      water: 0.45,    // ~45% water coverage (more realistic)
      grass: 0.65,    // ~20% grasslands/plains
      hills: 0.82,    // ~17% hills/forests
      mountains: 1.0  // ~18% mountains/high terrain
    }

    // Apply weight adjustments (each weight unit = 0.015 threshold shift)
    const weightFactor = 0.015
    const adjusted = {
      water: Math.max(0.1, Math.min(0.9, base.water + this.weights.water * weightFactor)),
      grass: Math.max(0.1, Math.min(0.9, base.grass + this.weights.grass * weightFactor)),
      hills: Math.max(0.1, Math.min(0.9, base.hills + this.weights.hills * weightFactor)),
      mountains: 1.0 // Mountains always go to the end
    }

    // Ensure proper ordering with smaller gaps for smoother transitions
    adjusted.grass = Math.max(adjusted.water + 0.03, adjusted.grass)
    adjusted.hills = Math.max(adjusted.grass + 0.03, adjusted.hills)

    return adjusted
  }

  getColor(value: number): THREE.Color {
    const thresholds = this.getTerrainThresholds()
    if (value < thresholds.water) return new THREE.Color(this.planetType.colors.water)
    if (value < thresholds.grass) return new THREE.Color(this.planetType.colors.grass)
    if (value < thresholds.hills) return new THREE.Color(this.planetType.colors.hills)
    return new THREE.Color(this.planetType.colors.mountains)
  }

  getHeight(value: number): number {
    const thresholds = this.getTerrainThresholds()
    const multipliers = this.planetType.heightMultipliers
    
    if (value < thresholds.water) {
      // More pronounced ocean depths based on how deep below water threshold
      const depth = (thresholds.water - value) / thresholds.water
      return multipliers.water * (1 + depth * 2) // Deeper areas get more depth
    }
    
    if (value < thresholds.grass) {
      // Coastal plains with slight elevation variation
      const elevation = (value - thresholds.water) / (thresholds.grass - thresholds.water)
      return multipliers.grass * (0.5 + elevation * 0.5)
    }
    
    if (value < thresholds.hills) {
      // Rolling hills with progressive height
      const hillHeight = (value - thresholds.grass) / (thresholds.hills - thresholds.grass)
      return multipliers.hills * (0.3 + hillHeight * 1.2)
    }
    
    // Mountains with dramatic height based on elevation above hills threshold
    const mountainHeight = (value - thresholds.hills) / (1.0 - thresholds.hills)
    return multipliers.mountains * (0.5 + mountainHeight * 2.0) // Higher peaks get much taller
  }

  generateTerrainValue(noise2D: (x: number, y: number) => number, lat: number, lon: number): number {
    // Convert spherical coordinates to 3D Cartesian for seamless noise sampling
    const x = Math.cos(lat) * Math.cos(lon)
    const y = Math.cos(lat) * Math.sin(lon)
    const z = Math.sin(lat)

    // Continental-scale features (major landmasses vs oceans)
    const continentNoise = noise2D(x * CONTINENT_SCALE, y * CONTINENT_SCALE) * 0.7

    // Regional terrain variation (mountain ranges, valleys, plains)
    const terrainNoise = noise2D(y * TERRAIN_SCALE, z * TERRAIN_SCALE) * 0.25

    // Local detail features (hills, small valleys)
    const detailNoise = noise2D(z * DETAIL_SCALE, x * DETAIL_SCALE) * 0.05

    // Combine layers with continental features dominating
    let combined = continentNoise + terrainNoise + detailNoise

    // Normalize to 0-1 range
    combined = (combined + 1) * 0.5

    // Create more realistic ocean/land distribution
    // Bias toward more ocean coverage (like Earth ~71% water)
    combined = combined * 0.8 + 0.1

    // Add latitude-based climate effects
    // More temperate zones in mid-latitudes, ice caps at poles
    const latitudeEffect = Math.cos(lat * 2.0) * 0.1
    combined += latitudeEffect

    // Add some polar ice cap effect (more water/ice at extreme latitudes)
    const polarEffect = Math.pow(Math.abs(Math.sin(lat)), 3) * -0.15
    combined += polarEffect

    return Math.max(0, Math.min(1, combined))
  }
}
