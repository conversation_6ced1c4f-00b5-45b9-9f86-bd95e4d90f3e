import { TerrainWeights, PlanetType, TerrainCoverage, TerrainPreset } from '@/types/terrain'

// Terrain generation constants
export const SPHERE_RADIUS = 25
export const SPHERE_SEGMENTS = 96     // Increased for better detail quality
export const CONTINENT_SCALE = 1.5    // Large scale for continent shapes
export const TERRAIN_SCALE = 4.0      // Medium scale for regional terrain
export const DETAIL_SCALE = 12.0      // Fine scale for local features

// Default terrain weights (legacy)
export const DEFAULT_WEIGHTS: TerrainWeights = {
  water: 0,
  grass: 0,
  hills: 0,
  mountains: 0
}

// Default terrain coverage for new system
export const DEFAULT_COVERAGE: TerrainCoverage = {
  oceanCoverage: 65,      // 65% ocean (Earth-like)
  plainsCoverage: 40,     // 40% of land is plains
  hillsCoverage: 35,      // 35% of land is hills
  mountainsCoverage: 25   // 25% of land is mountains (auto-calculated)
}

// Available planet types - Optimized for smooth color blending and enhanced terrain generation
export const PLANET_TYPES: Record<string, PlanetType> = {
  earth: {
    id: 'earth',
    name: 'Earth-like',
    colors: {
      water: '#1e3a8a',    // Deep ocean blue - darker for better depth variation
      grass: '#16a34a',    // Rich forest green - works well with elevation variation
      hills: '#78716c',    // Warm brown-gray - blends naturally with grass and mountains
      mountains: '#44403c' // Dark stone gray - creates good snow cap effect
    },
    heightMultipliers: {
      water: -2.2,   // Deeper oceans for more dramatic underwater terrain
      grass: 0.4,    // Slightly elevated coastal plains
      hills: 1.3,    // More pronounced rolling hills
      mountains: 2.8 // Majestic mountain peaks
    },
    description: 'Deep blue oceans, lush forests, weathered mountains'
  },
  alien: {
    id: 'alien',
    name: 'Alien World',
    colors: {
      water: '#064e3b',    // Dark emerald alien seas
      grass: '#92400e',    // Rust-red alien vegetation
      hills: '#451a03',    // Dark burnt sienna hills
      mountains: '#1c1917' // Nearly black obsidian peaks
    },
    heightMultipliers: {
      water: -3.5,   // Very deep alien abyssal seas
      grass: 0.6,    // Elevated alien plains with strange formations
      hills: 1.8,    // Dramatic alien hill formations
      mountains: 4.2 // Towering otherworldly spires
    },
    description: 'Emerald seas, rust-red terrain, obsidian spires'
  },
  crystalline: {
    id: 'crystalline',
    name: 'Crystalline World',
    colors: {
      water: '#0c4a6e',    // Deep frozen blue - creates ice depth effect
      grass: '#bae6fd',    // Pale ice blue - good base for crystal formations
      hills: '#38bdf8',    // Bright crystal blue - vibrant mid-tone
      mountains: '#0284c7' // Rich sapphire - dramatic peaks with good snow effect
    },
    heightMultipliers: {
      water: -1.8,   // Frozen seas with ice formations below surface
      grass: 0.3,    // Ice plains with subtle crystal growths
      hills: 1.5,    // Prominent crystal formations
      mountains: 3.2 // Towering crystalline spires
    },
    description: 'Frozen seas, crystal plains, sapphire spires'
  },
  volcanic: {
    id: 'volcanic',
    name: 'Volcanic World',
    colors: {
      water: '#b91c1c',    // Deep lava red - darker for molten depth effect
      grass: '#431407',    // Dark charred earth - good base for volcanic terrain
      hills: '#dc2626',    // Bright lava red - glowing volcanic activity
      mountains: '#fbbf24' // Bright yellow-orange - active volcanic peaks
    },
    heightMultipliers: {
      water: -1.2,   // Molten lava pools and flows
      grass: 0.5,    // Elevated ash plains and hardened lava fields
      hills: 2.0,    // Dramatic volcanic hills and cinder cones
      mountains: 3.8 // Massive active volcanoes
    },
    description: 'Molten seas, charred plains, erupting volcanoes'
  },
  desert: {
    id: 'desert',
    name: 'Desert World',
    colors: {
      water: '#0369a1',    // Deep oasis blue - creates precious water effect
      grass: '#d97706',    // Rich golden sand - warm desert base
      hills: '#f59e0b',    // Bright amber - sun-baked dunes
      mountains: '#7c2d12' // Deep rust red - ancient weathered mesas
    },
    heightMultipliers: {
      water: -0.8,   // Deeper oases and underground springs
      grass: 0.2,    // Vast sand flats and gentle dunes
      hills: 1.0,    // Rolling sand dunes and rock formations
      mountains: 2.2 // Weathered mesa formations and ancient peaks
    },
    description: 'Precious oases, golden dunes, ancient mesas'
  },
    toxic: {
      id: 'toxic',
      name: 'Toxic World',
      colors: {
        water: '#65a30d',    // acidic green pools
        grass: '#84cc16',    // toxic lime terrain
        hills: '#365314',    // dark poison green
        mountains: '#1a2e05' // venomous black peaks
      },
      heightMultipliers: {
        water: -2.5,   // acid lakes
        grass: 0.3,    // toxic plains
        hills: 1.3,    // poisonous hills
        mountains: 2.8 // deadly peaks
      },
      description: 'Acid pools, toxic plains, venomous peaks'
    },
  ethereal: {
    id: 'ethereal',
    name: 'Ethereal World',
    colors: {
      water: '#7c3aed',    // Deep mystical purple - creates magical depth
      grass: '#c4b5fd',    // Soft lavender - ethereal base terrain
      hills: '#a855f7',    // Vibrant amethyst - magical mid-elevations
      mountains: '#4c1d95' // Dark royal purple - ancient magical peaks
    },
    heightMultipliers: {
      water: -2.0,   // Deep mystical lakes and ethereal pools
      grass: 0.5,    // Floating plains with magical elevation
      hills: 1.4,    // Enchanted hills with mystical formations
      mountains: 2.6 // Towering magical spires and ancient temples
    },
    description: 'Mystical depths, ethereal plains, magical spires'
  },
  oceanic: {
    id: 'oceanic',
    name: 'Ocean World',
    colors: {
      water: '#0c4a6e',    // Deep abyssal blue - vast ocean depths
      grass: '#0ea5e9',    // Bright shallow blue - coral reef areas
      hills: '#38bdf8',    // Light blue - coral formations and atolls
      mountains: '#075985' // Dark blue-gray - rare volcanic islands
    },
    heightMultipliers: {
      water: -4.5,   // Vast deep oceans and abyssal trenches
      grass: -0.3,   // Shallow coral reefs just below surface
      hills: 0.4,    // Coral islands and reef formations
      mountains: 1.2 // Rare volcanic islands breaking the surface
    },
    description: 'Endless depths, coral reefs, volcanic islands'
  },
  arctic: {
    id: 'arctic',
    name: 'Arctic World',
    colors: {
      water: '#1e3a8a',    // Deep frozen blue - ice-covered seas
      grass: '#f1f5f9',    // Pure white - snow and ice plains
      hills: '#cbd5e1',    // Light gray - weathered ice formations
      mountains: '#475569' // Dark slate - ancient ice peaks
    },
    heightMultipliers: {
      water: -2.5,   // Frozen seas with deep ice formations
      grass: 0.3,    // Snow-covered plains and ice sheets
      hills: 1.2,    // Ice hills and glacial formations
      mountains: 2.4 // Massive ice-covered peaks
    },
    description: 'Frozen seas, snow plains, glacial peaks'
  },
  toxic: {
    id: 'toxic',
    name: 'Toxic World',
    colors: {
      water: '#365314',    // Dark toxic green - poisonous seas
      grass: '#84cc16',    // Bright acid green - toxic vegetation
      hills: '#65a30d',    // Medium green - contaminated hills
      mountains: '#1a2e05' // Very dark green - toxic waste peaks
    },
    heightMultipliers: {
      water: -1.8,   // Toxic pools and acid lakes
      grass: 0.4,    // Contaminated plains with toxic growths
      hills: 1.6,    // Hills of toxic waste and mutations
      mountains: 3.0 // Towering toxic spires and waste dumps
    },
    description: 'Toxic seas, acid plains, contaminated peaks'
  }

}

// Terrain presets for quick selection - Optimized for new planet types
export const TERRAIN_PRESETS: Record<string, TerrainPreset> = {
  earthlike: {
    id: 'earthlike',
    name: 'Earth-like',
    description: 'Balanced oceans and continents like Earth',
    coverage: {
      oceanCoverage: 65,
      plainsCoverage: 40,
      hillsCoverage: 35,
      mountainsCoverage: 25
    }
  },
  oceanic: {
    id: 'oceanic',
    name: 'Ocean World',
    description: 'Vast oceans with scattered islands',
    coverage: {
      oceanCoverage: 88,
      plainsCoverage: 50,
      hillsCoverage: 30,
      mountainsCoverage: 20
    }
  },
  continental: {
    id: 'continental',
    name: 'Continental',
    description: 'Large landmasses with smaller seas',
    coverage: {
      oceanCoverage: 35,
      plainsCoverage: 50,
      hillsCoverage: 30,
      mountainsCoverage: 20
    }
  },
  mountainous: {
    id: 'mountainous',
    name: 'Mountainous',
    description: 'Rugged terrain with towering peaks',
    coverage: {
      oceanCoverage: 45,
      plainsCoverage: 15,
      hillsCoverage: 35,
      mountainsCoverage: 50
    }
  },
  desert: {
    id: 'desert',
    name: 'Desert World',
    description: 'Arid plains with rare water sources',
    coverage: {
      oceanCoverage: 12,
      plainsCoverage: 75,
      hillsCoverage: 18,
      mountainsCoverage: 7
    }
  },
  arctic: {
    id: 'arctic',
    name: 'Arctic World',
    description: 'Frozen landscape with ice formations',
    coverage: {
      oceanCoverage: 70,
      plainsCoverage: 60,
      hillsCoverage: 25,
      mountainsCoverage: 15
    }
  },
  volcanic: {
    id: 'volcanic',
    name: 'Volcanic World',
    description: 'Active volcanic terrain with lava flows',
    coverage: {
      oceanCoverage: 40,
      plainsCoverage: 25,
      hillsCoverage: 40,
      mountainsCoverage: 35
    }
  }
}

// Storage key for local storage
export const STORAGE_KEY = 'terrain-map-settings'
