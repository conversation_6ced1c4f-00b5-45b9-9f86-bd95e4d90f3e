import { TerrainWeights, PlanetType } from '@/types/terrain'

// Terrain generation constants
export const SPHERE_RADIUS = 25
export const SPHERE_SEGMENTS = 64
export const CONTINENT_SCALE = 1.5    // Large scale for continent shapes
export const TERRAIN_SCALE = 4.0      // Medium scale for regional terrain
export const DETAIL_SCALE = 12.0      // Fine scale for local features

// Default terrain weights
export const DEFAULT_WEIGHTS: TerrainWeights = {
  water: 0,
  grass: 0,
  hills: 0,
  mountains: 0
}

// Available planet types
export const PLANET_TYPES: Record<string, PlanetType> = {
  earth: {
    id: 'earth',
    name: 'Earth-like',
    colors: {
      water: '#1e40af',    // deep ocean blue
      grass: '#22c55e',    // vibrant grass green
      hills: '#a3a3a3',    // brownish hills
      mountains: '#525252' // dark mountain gray
    },
    heightMultipliers: {
      water: -2.0,   // deeper oceans
      grass: 0.3,    // coastal plains
      hills: 1.0,    // rolling hills
      mountains: 2.5 // high peaks
    },
    description: 'Blue oceans, green vegetation, rocky mountains'
  },
    alien: {
      id: 'alien',
      name: 'Alien World',
      colors: {
        water: '#065f46',    // deep green water
        grass: '#a14121',    // reddish-brown terrain
        hills: '#242424',    // dark brown hills
        mountains: '#451a03' // obsidian black mountains
      },
      heightMultipliers: {
        water: -3.0,   // very deep alien seas
        grass: 0.5,    // elevated alien plains
        hills: 1.5,    // prominent alien hills
        mountains: 4.0 // towering alien peaks
      },
      description: 'Green seas, alien terrain, obsidian peaks'
    },
    crystalline: {
      id: 'crystalline',
      name: 'Crystalline World',
      colors: {
        water: '#0ea5e9',    // bright cyan ice
        grass: '#e0f2fe',    // pale ice blue
        hills: '#7dd3fc',    // light crystal blue
        mountains: '#0284c7' // deep sapphire peaks
      },
      heightMultipliers: {
        water: -1.5,   // frozen seas
        grass: 0.2,    // ice plains
        hills: 1.2,    // crystal formations
        mountains: 3.0 // towering ice spires
      },
      description: 'Frozen seas, crystalline plains, sapphire peaks'
    },
    volcanic: {
      id: 'volcanic',
      name: 'Volcanic World',
      colors: {
        water: '#dc2626',    // molten lava red
        grass: '#451a03',    // charred earth
        hills: '#ea580c',    // glowing ember orange
        mountains: '#fbbf24' // bright lava yellow
      },
      heightMultipliers: {
        water: -1.0,   // lava pools
        grass: 0.4,    // ash plains
        hills: 1.8,    // volcanic hills
        mountains: 3.5 // active volcanoes
      },
      description: 'Lava seas, ash plains, erupting volcanoes'
    }, 
    desert: {
      id: 'desert',
      name: 'Desert World',
      colors: {
        water: '#0891b2',    // rare oasis blue
        grass: '#fbbf24',    // golden sand
        hills: '#f59e0b',    // amber dunes
        mountains: '#92400e' // rust red mesas
      },
      heightMultipliers: {
        water: -0.5,   // shallow oases
        grass: 0.1,    // sand flats
        hills: 0.8,    // rolling dunes
        mountains: 2.0 // mesa formations
      },
      description: 'Rare oases, golden dunes, rust mesas'
    },
    toxic: {
      id: 'toxic',
      name: 'Toxic World',
      colors: {
        water: '#65a30d',    // acidic green pools
        grass: '#84cc16',    // toxic lime terrain
        hills: '#365314',    // dark poison green
        mountains: '#1a2e05' // venomous black peaks
      },
      heightMultipliers: {
        water: -2.5,   // acid lakes
        grass: 0.3,    // toxic plains
        hills: 1.3,    // poisonous hills
        mountains: 2.8 // deadly peaks
      },
      description: 'Acid pools, toxic plains, venomous peaks'
    },
    ethereal: {
      id: 'ethereal',
      name: 'Ethereal World',
      colors: {
        water: '#8b5cf6',    // mystical purple
        grass: '#c084fc',    // lavender fields
        hills: '#a855f7',    // amethyst hills
        mountains: '#581c87' // deep violet peaks
      },
      heightMultipliers: {
        water: -1.8,   // mystical lakes
        grass: 0.4,    // floating plains
        hills: 1.1,    // enchanted hills
        mountains: 2.2 // magical spires
      },
      description: 'Mystical waters, lavender fields, violet spires'
    }

}

// Storage key for local storage
export const STORAGE_KEY = 'terrain-map-settings'
