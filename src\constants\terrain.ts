import { TerrainWeights, PlanetType } from '@/types/terrain'

// Terrain generation constants
export const SPHERE_RADIUS = 25
export const SPHERE_SEGMENTS = 64
export const CONTINENT_SCALE = 1.5    // Large scale for continent shapes
export const TERRAIN_SCALE = 4.0      // Medium scale for regional terrain
export const DETAIL_SCALE = 12.0      // Fine scale for local features

// Default terrain weights
export const DEFAULT_WEIGHTS: TerrainWeights = {
  water: 0,
  grass: 0,
  hills: 0,
  mountains: 0
}

// Available planet types
export const PLANET_TYPES: Record<string, PlanetType> = {
  earth: {
    id: 'earth',
    name: 'Earth-like',
    colors: {
      water: '#1e40af',    // deep ocean blue
      grass: '#22c55e',    // vibrant grass green
      hills: '#a3a3a3',    // brownish hills
      mountains: '#525252' // dark mountain gray
    },
    heightMultipliers: {
      water: -2.0,   // deeper oceans
      grass: 0.3,    // coastal plains
      hills: 1.0,    // rolling hills
      mountains: 2.5 // high peaks
    },
    description: 'Blue oceans, green vegetation, rocky mountains'
  },
  alien: {
    id: 'alien',
    name: 'Alien World',
    colors: {
      water: '#065f46',    // deep green water
      grass: '#7c2d12',    // reddish-brown terrain
      hills: '#451a03',    // dark brown hills
      mountains: '#0c0a09' // obsidian black mountains
    },
    heightMultipliers: {
      water: -3.0,   // very deep alien seas
      grass: 0.5,    // elevated alien plains
      hills: 1.5,    // prominent alien hills
      mountains: 4.0 // towering alien peaks
    },
    description: 'Green seas, alien terrain, obsidian peaks'
  }
}

// Storage key for local storage
export const STORAGE_KEY = 'terrain-map-settings'
