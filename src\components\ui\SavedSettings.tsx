import { useState } from 'react'
import { SavedSetting, TerrainWeights, PlanetType } from '@/types/terrain'

interface SavedSettingsProps {
  savedSettings: SavedSetting[]
  onSave: (name: string) => void
  onLoad: (setting: SavedSetting) => void
  onDelete: (id: string) => void
}

export function SavedSettings({ savedSettings, onSave, onLoad, onDelete }: SavedSettingsProps) {
  const [saveDialogOpen, setSaveDialogOpen] = useState(false)
  const [saveName, setSaveName] = useState('')

  const handleSave = () => {
    if (saveName.trim()) {
      onSave(saveName.trim())
      setSaveName('')
      setSaveDialogOpen(false)
    }
  }

  return (
    <div className="mb-6">
      <h2 className="text-lg font-semibold mb-3 text-gray-200">Saved Settings</h2>

      <button
        onClick={() => setSaveDialogOpen(true)}
        className="w-full px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm mb-3 font-medium"
      >
        Save Current Settings
      </button>

      {saveDialogOpen && (
        <div className="mb-3 p-3 border border-gray-600 rounded bg-gray-700">
          <input
            type="text"
            value={saveName}
            onChange={(e) => setSaveName(e.target.value)}
            placeholder="Enter setting name..."
            className="w-full px-2 py-1 border border-gray-600 rounded text-sm mb-2 bg-gray-800 text-white placeholder-gray-400 focus:border-blue-400 focus:outline-none"
            onKeyPress={(e) => e.key === 'Enter' && handleSave()}
          />
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              className="px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 transition-colors"
            >
              Save
            </button>
            <button
              onClick={() => {
                setSaveDialogOpen(false)
                setSaveName('')
              }}
              className="px-3 py-1 bg-gray-600 text-white rounded text-xs hover:bg-gray-500 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      <div className="space-y-2 max-h-40 overflow-y-auto">
        {savedSettings.map((setting) => (
          <div key={setting.id} className="flex items-center justify-between p-2 border border-gray-600 rounded bg-gray-700">
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-gray-200 truncate">{setting.name}</div>
              <div className="text-xs text-gray-400">
                {new Date(setting.createdAt).toLocaleDateString()}
              </div>
            </div>
            <div className="flex space-x-1 ml-2">
              <button
                onClick={() => onLoad(setting)}
                className="px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 transition-colors"
              >
                Load
              </button>
              <button
                onClick={() => onDelete(setting.id)}
                className="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
