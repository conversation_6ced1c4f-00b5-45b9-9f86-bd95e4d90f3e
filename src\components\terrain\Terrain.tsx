import { useMemo } from 'react'
import { createNoise2D } from 'simplex-noise'
import * as THREE from 'three'
import { TerrainWeights, TerrainCoverage, PlanetType } from '@/types/terrain'
import { SPHERE_RADIUS, SPHERE_SEGMENTS } from '@/constants/terrain'
import { TerrainGenerator } from '@/lib/terrain-generator'

interface TerrainProps {
  weights: TerrainWeights
  coverage?: TerrainCoverage
  seed: string
  planetType: PlanetType
}

export function Terrain({ weights, coverage, seed, planetType }: TerrainProps) {
  const geometry = useMemo(() => {
    // Create terrain generator instance
    const terrainGen = new TerrainGenerator(planetType, weights, coverage)
    
    // Create seeded noise generator using the seed
    const seedHash = seed.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    
    const noise2D = createNoise2D(() => Math.sin(seedHash * 0.1))
    const geometry = new THREE.SphereGeometry(SPHERE_RADIUS, SPHERE_SEGMENTS, SPHERE_SEGMENTS)
    const vertices = geometry.attributes.position.array as Float32Array
    const colors = new Float32Array(vertices.length)

    // Modify vertices to create height-based terrain on sphere
    for (let i = 0; i < vertices.length; i += 3) {
      const x = vertices[i]
      const y = vertices[i + 1]
      const z = vertices[i + 2]

      // Convert Cartesian coordinates to spherical coordinates
      const radius = Math.sqrt(x * x + y * y + z * z)
      const lat = Math.asin(z / radius) // latitude (-π/2 to π/2)
      const lon = Math.atan2(y, x)      // longitude (-π to π)

      // Generate terrain value using terrain generator
      const terrainValue = terrainGen.generateTerrainValue(noise2D, lat, lon)

      // Calculate height displacement using terrain generator
      const heightDisplacement = terrainGen.getHeight(terrainValue)
      
      // Apply displacement along the normal (radial direction)
      const newRadius = SPHERE_RADIUS + heightDisplacement
      
      vertices[i] = (x / radius) * newRadius
      vertices[i + 1] = (y / radius) * newRadius
      vertices[i + 2] = (z / radius) * newRadius

      // Set colors based on terrain value using terrain generator
      const color = terrainGen.getColor(terrainValue)
      colors[i] = color.r
      colors[i + 1] = color.g
      colors[i + 2] = color.b
    }

    geometry.attributes.position.needsUpdate = true
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
    geometry.computeVertexNormals()

    return geometry
  }, [weights, coverage, seed, planetType])

  return (
    <mesh geometry={geometry}>
      <meshPhongMaterial
        vertexColors
        shininess={30}
        specular={0x111111}
        transparent={false}
      />
    </mesh>
  )
}
