'use client'

import { useState } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls } from '@react-three/drei'

// Components
import { Terrain } from '@/components/terrain'
import { PlanetTypeSelector, TerrainControls, TerrainCoverageControls, SavedSettings, HelpText } from '@/components/ui'

// Hooks
import { useTerrainSettings } from '@/hooks/useTerrainSettings'

// Constants
import { PLANET_TYPES, TERRAIN_PRESETS } from '@/constants/terrain'

export default function MapPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const {
    weights,
    coverage,
    currentPlanetType,
    savedSettings,
    currentSeed,
    useCoverageSystem,
    updateWeight,
    updateCoverage,
    generateNewMap,
    saveCurrentSettings,
    loadSettings,
    deleteSettings,
    setCurrentPlanetType,
    setUseCoverageSystem
  } = useTerrainSettings()

  return (
    <div className="h-screen flex bg-gray-900">
      {/* Hamburger Menu <PERSON> */}
      <button
        onClick={() => setSidebarOpen(!sidebarOpen)}
        className="fixed top-4 left-4 z-50 p-2 bg-gray-800 rounded-lg shadow-lg hover:bg-gray-700 transition-colors border border-gray-600"
      >
        <div className="w-6 h-6 flex flex-col justify-center space-y-1">
          <div className={`h-0.5 bg-gray-300 transition-all ${sidebarOpen ? 'rotate-45 translate-y-1.5' : ''}`}></div>
          <div className={`h-0.5 bg-gray-300 transition-all ${sidebarOpen ? 'opacity-0' : ''}`}></div>
          <div className={`h-0.5 bg-gray-300 transition-all ${sidebarOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></div>
        </div>
      </button>

      {/* Sidebar */}
      <div className={`fixed left-0 top-0 h-full bg-gray-800 shadow-xl transition-transform duration-300 z-40 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } w-80 overflow-y-auto border-r border-gray-700`}>
        <div className="p-6 pt-16">
          <h1 className="text-2xl font-bold mb-6 text-white">3D Planet Generator</h1>

          <PlanetTypeSelector
            planetTypes={PLANET_TYPES}
            currentPlanetType={currentPlanetType}
            onPlanetTypeChange={setCurrentPlanetType}
          />

          {useCoverageSystem ? (
            <TerrainCoverageControls
              coverage={coverage}
              planetType={currentPlanetType}
              onCoverageChange={updateCoverage}
              onGenerateNew={generateNewMap}
              presets={TERRAIN_PRESETS}
            />
          ) : (
            <TerrainControls
              weights={weights}
              onWeightChange={updateWeight}
              onGenerateNew={generateNewMap}
            />
          )}

          {/* System Toggle */}
          <div className="mb-6 p-3 border border-gray-600 rounded bg-gray-700">
            <label className="flex items-center text-sm text-gray-300">
              <input
                type="checkbox"
                checked={useCoverageSystem}
                onChange={(e) => setUseCoverageSystem(e.target.checked)}
                className="mr-2"
              />
              Use new coverage system (recommended)
            </label>
          </div>

          <SavedSettings
            savedSettings={savedSettings}
            onSave={saveCurrentSettings}
            onLoad={loadSettings}
            onDelete={deleteSettings}
          />

          <HelpText currentPlanetType={currentPlanetType} />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 bg-gray-900">
        <Canvas camera={{ position: [50, 30, 50], fov: 60 }}>
          <ambientLight intensity={0.3} />
          <directionalLight position={[10, 10, 5]} intensity={0.8} castShadow />
          <directionalLight position={[-5, -5, -10]} intensity={0.2} color="#4a90e2" />
          <pointLight position={[0, 0, 0]} intensity={0.1} color="#ffffff" />
          <Terrain
            weights={weights}
            coverage={useCoverageSystem ? coverage : undefined}
            seed={currentSeed}
            planetType={currentPlanetType}
          />
          <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
        </Canvas>
      </div>
    </div>
  )
}
