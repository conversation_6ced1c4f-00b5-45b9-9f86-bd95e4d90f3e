// Terrain-related type definitions

export interface TerrainWeights {
  water: number
  grass: number
  hills: number
  mountains: number
}

// New percentage-based terrain coverage system
export interface TerrainCoverage {
  oceanCoverage: number      // 0-90: Percentage of planet covered by water
  plainsCoverage: number     // 0-100: Percentage of remaining land that is plains/lowlands
  hillsCoverage: number      // 0-100: Percentage of remaining land that is hills/forests
  mountainsCoverage: number  // 0-100: Percentage of remaining land that is mountains (auto-calculated)
}

// Terrain randomness control system
export interface TerrainRandomness {
  continentalStructure: number  // 0-100: 0 = completely random, 100 = structured continents
  noiseComplexity: number       // 0-100: How complex/chaotic the terrain patterns are
  featureSize: number           // 0-100: Size of terrain features (0 = small, 100 = large)
}

// Preset terrain configurations for quick selection
export interface TerrainPreset {
  id: string
  name: string
  description: string
  coverage: TerrainCoverage
}

export interface PlanetType {
  id: string
  name: string
  colors: {
    water: string
    grass: string
    hills: string
    mountains: string
  }
  heightMultipliers: {
    water: number
    grass: number
    hills: number
    mountains: number
  }
  description: string
}

export interface SavedSetting {
  id: string
  name: string
  weights: TerrainWeights
  seed: string
  createdAt: string
}

// New saved setting format for coverage-based system
export interface SavedCoverageSetting {
  id: string
  name: string
  coverage: TerrainCoverage
  planetTypeId: string
  seed: string
  createdAt: string
}
