// Terrain-related type definitions

export interface TerrainWeights {
  water: number
  grass: number
  hills: number
  mountains: number
}

export interface PlanetType {
  id: string
  name: string
  colors: {
    water: string
    grass: string
    hills: string
    mountains: string
  }
  heightMultipliers: {
    water: number
    grass: number
    hills: number
    mountains: number
  }
  description: string
}

export interface SavedSetting {
  id: string
  name: string
  weights: TerrainWeights
  seed: string
  createdAt: string
}
