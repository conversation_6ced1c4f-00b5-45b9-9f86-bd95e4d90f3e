import { TerrainRandomness } from '@/types/terrain'

interface TerrainRandomnessControlsProps {
  randomness: TerrainRandomness
  onRandomnessChange: (field: keyof TerrainRandomness, value: number) => void
}

export function TerrainRandomnessControls({ 
  randomness, 
  onRandomnessChange 
}: TerrainRandomnessControlsProps) {
  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-200 mb-3">Terrain Randomness</h3>
      <div className="space-y-4">
        
        {/* Continental Structure */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <label className="text-sm font-medium text-gray-300">
              Continental Structure
            </label>
            <span className="text-xs text-gray-400">
              {randomness.continentalStructure}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={randomness.continentalStructure}
            onChange={(e) => onRandomnessChange('continentalStructure', parseInt(e.target.value))}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer
                       [&::-webkit-slider-thumb]:appearance-none
                       [&::-webkit-slider-thumb]:w-4
                       [&::-webkit-slider-thumb]:h-4
                       [&::-webkit-slider-thumb]:rounded-full
                       [&::-webkit-slider-thumb]:bg-blue-500
                       [&::-webkit-slider-thumb]:cursor-pointer
                       [&::-webkit-slider-thumb]:shadow-lg
                       [&::-webkit-slider-thumb]:border-2
                       [&::-webkit-slider-thumb]:border-blue-400
                       [&::-moz-range-thumb]:w-4
                       [&::-moz-range-thumb]:h-4
                       [&::-moz-range-thumb]:rounded-full
                       [&::-moz-range-thumb]:bg-blue-500
                       [&::-moz-range-thumb]:cursor-pointer
                       [&::-moz-range-thumb]:border-2
                       [&::-moz-range-thumb]:border-blue-400"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Random Oceans & Land</span>
            <span>Structured Continents</span>
          </div>
        </div>

        {/* Noise Complexity */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <label className="text-sm font-medium text-gray-300">
              Terrain Complexity
            </label>
            <span className="text-xs text-gray-400">
              {randomness.noiseComplexity}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={randomness.noiseComplexity}
            onChange={(e) => onRandomnessChange('noiseComplexity', parseInt(e.target.value))}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer
                       [&::-webkit-slider-thumb]:appearance-none
                       [&::-webkit-slider-thumb]:w-4
                       [&::-webkit-slider-thumb]:h-4
                       [&::-webkit-slider-thumb]:rounded-full
                       [&::-webkit-slider-thumb]:bg-green-500
                       [&::-webkit-slider-thumb]:cursor-pointer
                       [&::-webkit-slider-thumb]:shadow-lg
                       [&::-webkit-slider-thumb]:border-2
                       [&::-webkit-slider-thumb]:border-green-400
                       [&::-moz-range-thumb]:w-4
                       [&::-moz-range-thumb]:h-4
                       [&::-moz-range-thumb]:rounded-full
                       [&::-moz-range-thumb]:bg-green-500
                       [&::-moz-range-thumb]:cursor-pointer
                       [&::-moz-range-thumb]:border-2
                       [&::-moz-range-thumb]:border-green-400"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Simple</span>
            <span>Highly Detailed</span>
          </div>
        </div>

        {/* Feature Size */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <label className="text-sm font-medium text-gray-300">
              Feature Size
            </label>
            <span className="text-xs text-gray-400">
              {randomness.featureSize}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={randomness.featureSize}
            onChange={(e) => onRandomnessChange('featureSize', parseInt(e.target.value))}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer
                       [&::-webkit-slider-thumb]:appearance-none
                       [&::-webkit-slider-thumb]:w-4
                       [&::-webkit-slider-thumb]:h-4
                       [&::-webkit-slider-thumb]:rounded-full
                       [&::-webkit-slider-thumb]:bg-purple-500
                       [&::-webkit-slider-thumb]:cursor-pointer
                       [&::-webkit-slider-thumb]:shadow-lg
                       [&::-webkit-slider-thumb]:border-2
                       [&::-webkit-slider-thumb]:border-purple-400
                       [&::-moz-range-thumb]:w-4
                       [&::-moz-range-thumb]:h-4
                       [&::-moz-range-thumb]:rounded-full
                       [&::-moz-range-thumb]:bg-purple-500
                       [&::-moz-range-thumb]:cursor-pointer
                       [&::-moz-range-thumb]:border-2
                       [&::-moz-range-thumb]:border-purple-400"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Small Features</span>
            <span>Large Features</span>
          </div>
        </div>

        {/* Quick Presets */}
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-300 mb-2">Quick Presets</h4>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => {
                onRandomnessChange('continentalStructure', 85)
                onRandomnessChange('noiseComplexity', 40)
                onRandomnessChange('featureSize', 75)
              }}
              className="px-3 py-2 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 transition-colors"
            >
              Earth-like
            </button>
            <button
              onClick={() => {
                onRandomnessChange('continentalStructure', 15)
                onRandomnessChange('noiseComplexity', 80)
                onRandomnessChange('featureSize', 30)
              }}
              className="px-3 py-2 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors"
            >
              Chaotic
            </button>
            <button
              onClick={() => {
                onRandomnessChange('continentalStructure', 50)
                onRandomnessChange('noiseComplexity', 90)
                onRandomnessChange('featureSize', 20)
              }}
              className="px-3 py-2 bg-green-600 text-white rounded text-xs hover:bg-green-700 transition-colors"
            >
              Detailed
            </button>
            <button
              onClick={() => {
                onRandomnessChange('continentalStructure', 70)
                onRandomnessChange('noiseComplexity', 30)
                onRandomnessChange('featureSize', 85)
              }}
              className="px-3 py-2 bg-purple-600 text-white rounded text-xs hover:bg-purple-700 transition-colors"
            >
              Simple
            </button>
          </div>
        </div>

      </div>
    </div>
  )
}
