# Project Structure

This document outlines the refactored structure of the 3D Planet Generator application.

## Directory Structure

```
src/
├── app/
│   └── map/
│       └── page.tsx              # Main page component (clean, focused on rendering)
├── components/
│   ├── terrain/
│   │   ├── Terrain.tsx           # 3D terrain rendering component
│   │   └── index.ts              # Barrel export
│   └── ui/
│       ├── PlanetTypeSelector.tsx # Planet type selection UI
│       ├── TerrainControls.tsx   # Terrain weight controls
│       ├── SavedSettings.tsx     # Save/load settings UI
│       ├── HelpText.tsx          # Help and color preview
│       └── index.ts              # Barrel exports
├── hooks/
│   └── useTerrainSettings.ts     # Custom hook for terrain state management
├── lib/
│   └── terrain-generator.ts      # TerrainGenerator class for planet generation
├── utils/
│   └── storage.ts                # Local storage utility functions
├── types/
│   └── terrain.ts                # TypeScript type definitions
└── constants/
    └── terrain.ts                # Constants and planet type definitions
```

## Key Components

### `src/app/map/page.tsx`
- **Purpose**: Main page component focused purely on rendering and layout
- **Responsibilities**: UI layout, sidebar management, component orchestration
- **Dependencies**: Uses custom hook and imported components

### `src/hooks/useTerrainSettings.ts`
- **Purpose**: Custom hook managing all terrain-related state and logic
- **Responsibilities**: State management, event handlers, local storage integration
- **Returns**: All necessary state and functions for terrain manipulation

### `src/lib/terrain-generator.ts`
- **Purpose**: Core terrain generation logic
- **Responsibilities**: Planet type-specific terrain generation, height/color calculation
- **Features**: Extensible class system for different planet types

### `src/components/terrain/Terrain.tsx`
- **Purpose**: 3D terrain rendering using Three.js
- **Responsibilities**: Geometry generation, vertex manipulation, material application
- **Dependencies**: Uses TerrainGenerator for terrain logic

### `src/components/ui/`
- **Purpose**: Reusable UI components for the sidebar
- **Components**:
  - `PlanetTypeSelector`: Planet type selection interface
  - `TerrainControls`: Weight adjustment controls
  - `SavedSettings`: Save/load functionality
  - `HelpText`: User guidance and color preview

### `src/utils/storage.ts`
- **Purpose**: Local storage utility functions
- **Functions**: `loadSavedSettings()`, `saveSetting()`, `deleteSetting()`
- **Features**: Error handling, type safety

### `src/types/terrain.ts`
- **Purpose**: TypeScript type definitions
- **Types**: `TerrainWeights`, `PlanetType`, `SavedSetting`
- **Benefits**: Type safety across the application

### `src/constants/terrain.ts`
- **Purpose**: Application constants and configuration
- **Contents**: Sphere parameters, noise scales, default weights, planet type definitions
- **Benefits**: Centralized configuration, easy to modify

## Benefits of This Structure

1. **Separation of Concerns**: Each file has a single, clear responsibility
2. **Reusability**: Components and utilities can be easily reused
3. **Maintainability**: Changes to specific functionality are isolated
4. **Type Safety**: Centralized type definitions ensure consistency
5. **Testability**: Individual components and utilities can be tested in isolation
6. **Scalability**: Easy to add new planet types, UI components, or features

## Adding New Planet Types

To add a new planet type:

1. Add the planet definition to `PLANET_TYPES` in `src/constants/terrain.ts`
2. The UI will automatically pick up the new planet type
3. No changes needed to other components

## Adding New UI Components

1. Create the component in `src/components/ui/`
2. Export it from `src/components/ui/index.ts`
3. Import and use in the main page component
