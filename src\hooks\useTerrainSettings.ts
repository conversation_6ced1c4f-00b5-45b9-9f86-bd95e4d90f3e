import { useState, useEffect, useCallback } from 'react'
import { TerrainWeights, PlanetType, SavedSetting } from '@/types/terrain'
import { DEFAULT_WEIGHTS, PLANET_TYPES } from '@/constants/terrain'
import { loadSavedSettings, saveSetting, deleteSetting } from '@/utils/storage'

export function useTerrainSettings() {
  const [weights, setWeights] = useState<TerrainWeights>(DEFAULT_WEIGHTS)
  const [currentPlanetType, setCurrentPlanetType] = useState<PlanetType>(PLANET_TYPES.earth)
  const [savedSettings, setSavedSettings] = useState<SavedSetting[]>([])
  const [currentSeed, setCurrentSeed] = useState<string>(() => Math.random().toString(36).substring(2, 15))

  // Load saved settings on component mount
  useEffect(() => {
    setSavedSettings(loadSavedSettings())
  }, [])

  const updateWeight = useCallback((terrain: keyof TerrainWeights, delta: number) => {
    setWeights(prev => ({
      ...prev,
      [terrain]: Math.max(-50, Math.min(50, prev[terrain] + delta))
    }))
  }, [])

  const generateNewMap = useCallback(() => {
    setCurrentSeed(Math.random().toString(36).substring(2, 15))
  }, [])

  const saveCurrentSettings = useCallback((name: string) => {
    const newSetting: SavedSetting = {
      id: Date.now().toString(),
      name,
      weights: { ...weights },
      seed: currentSeed,
      createdAt: new Date().toISOString()
    }
    
    saveSetting(newSetting)
    setSavedSettings(loadSavedSettings())
  }, [weights, currentSeed])

  const loadSettings = useCallback((setting: SavedSetting) => {
    setWeights(setting.weights)
    setCurrentSeed(setting.seed)
  }, [])

  const deleteSettings = useCallback((id: string) => {
    deleteSetting(id)
    setSavedSettings(loadSavedSettings())
  }, [])

  return {
    weights,
    currentPlanetType,
    savedSettings,
    currentSeed,
    updateWeight,
    generateNewMap,
    saveCurrentSettings,
    loadSettings,
    deleteSettings,
    setCurrentPlanetType
  }
}
