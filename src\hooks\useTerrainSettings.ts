import { useState, useEffect, useCallback } from 'react'
import { TerrainWeights, TerrainCoverage, PlanetType, SavedSetting, SavedCoverageSetting, TerrainRandomness } from '@/types/terrain'
import { DEFAULT_WEIGHTS, DEFAULT_COVERAGE, DEFAULT_RANDOMNESS, PLANET_TYPES } from '@/constants/terrain'
import { loadSavedSettings, saveSetting, deleteSetting } from '@/utils/storage'

export function useTerrainSettings() {
  const [weights, setWeights] = useState<TerrainWeights>(DEFAULT_WEIGHTS)
  const [coverage, setCoverage] = useState<TerrainCoverage>(DEFAULT_COVERAGE)
  const [randomness, setRandomness] = useState<TerrainRandomness>(DEFAULT_RANDOMNESS)
  const [currentPlanetType, setCurrentPlanetType] = useState<PlanetType>(PLANET_TYPES.earth)
  const [savedSettings, setSavedSettings] = useState<SavedSetting[]>([])
  const [currentSeed, setCurrentSeed] = useState<string>(() => Math.random().toString(36).substring(2, 15))
  const [useCoverageSystem, setUseCoverageSystem] = useState<boolean>(true) // Default to new system

  // Load saved settings on component mount
  useEffect(() => {
    setSavedSettings(loadSavedSettings())
  }, [])

  const updateWeight = useCallback((terrain: keyof TerrainWeights, delta: number) => {
    setWeights(prev => ({
      ...prev,
      [terrain]: Math.max(-50, Math.min(50, prev[terrain] + delta))
    }))
  }, [])

  const updateCoverage = useCallback((newCoverage: TerrainCoverage) => {
    setCoverage(newCoverage)
  }, [])

  const updateRandomness = useCallback((field: keyof TerrainRandomness, value: number) => {
    setRandomness(prev => ({
      ...prev,
      [field]: Math.max(0, Math.min(100, value))
    }))
  }, [])

  const generateNewMap = useCallback(() => {
    setCurrentSeed(Math.random().toString(36).substring(2, 15))
  }, [])

  const saveCurrentSettings = useCallback((name: string) => {
    const newSetting: SavedSetting = {
      id: Date.now().toString(),
      name,
      weights: { ...weights },
      seed: currentSeed,
      createdAt: new Date().toISOString()
    }
    
    saveSetting(newSetting)
    setSavedSettings(loadSavedSettings())
  }, [weights, currentSeed])

  const loadSettings = useCallback((setting: SavedSetting) => {
    setWeights(setting.weights)
    setCurrentSeed(setting.seed)
  }, [])

  const deleteSettings = useCallback((id: string) => {
    deleteSetting(id)
    setSavedSettings(loadSavedSettings())
  }, [])

  return {
    weights,
    coverage,
    randomness,
    currentPlanetType,
    savedSettings,
    currentSeed,
    useCoverageSystem,
    updateWeight,
    updateCoverage,
    updateRandomness,
    generateNewMap,
    saveCurrentSettings,
    loadSettings,
    deleteSettings,
    setCurrentPlanetType,
    setUseCoverageSystem
  }
}
