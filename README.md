# 🌍 Procedural Map Generator (Proof of Concept)

This is a simple procedural terrain map generator built using **Next.js 15 (App Router)**, **TypeScript**, and the [`simplex-noise`](https://www.npmjs.com/package/simplex-noise) package.

It generates a 2D tile-based world using noise values and renders it on an HTML5 `<canvas>` element using basic color coding to represent biomes like water, grass, hills, and mountains.

---

## 🚀 Features

- 📦 Built with **Next.js 15** (App Directory structure)
- 🧠 Uses **Simplex Noise** for smooth, organic-looking terrain generation
- 🖼️ Renders a top-down 2D map using HTML5 Canvas
- 🌐 100x100 grid with scalable tile size
- 🌈 Visualizes height values using color-coded tiles:
  - Deep Water: `#0066cc`
  - Grassland: `#33cc33`
  - Hills: `#aaaa00`
  - Mountains: `#888888`

---

## 📁 Project Structure

simplex-map-gen/
├── app/
│ ├── map/
│ │ └── page.tsx # Main map rendering logic
│ └── page.tsx # Homepage with link to map
├── public/ # Static files (not used yet)
├── styles/ # Tailwind styles if configured
├── package.json
└── README.md

---

## 🛠️ Installation

# 1. Clone the repo
git clone https://github.com/your-username/simplex-map-gen.git
cd simplex-map-gen

# 2. Install dependencies
npm install

# 3. Run the dev server
npm run dev
Then open http://localhost:3000/map in your browser.

✨ How It Works
1. Noise Generation
We use simplex-noise to generate values between -1 and 1, then normalize them to the 0–1 range:
const value = simplex.noise2D(x * scale, y * scale) * 0.5 + 0.5

2. Color Mapping
Based on the normalized noise value, we assign colors to tiles:

Noise Range	Terrain Type	Color Code
0.0–0.3	Water	#0066cc
0.3–0.5	Grassland	#33cc33
0.5–0.7	Hills	#aaaa00
0.7–1.0	Mountains	#888888

3. Rendering
Each tile is drawn using Canvas 2D’s fillRect method:
ctx.fillRect(x * TILE_SIZE, y * TILE_SIZE, TILE_SIZE, TILE_SIZE)
🧩 Configuration
You can tweak these constants in app/map/page.tsx:

const WIDTH = 100         // Map width in tiles
const HEIGHT = 100        // Map height in tiles
const TILE_SIZE = 8       // Size of each tile in pixels
const SCALE = 0.1         // Noise scale (zoom)
Smaller SCALE values = smoother terrain.
Larger SCALE values = more variation, sharper features.

🔮 Future Ideas
✅ Regenerate map on button click

🎲 Use custom seeds for reproducible maps

🗺️ Add more biome types (desert, tundra, forests)

🧱 Export map as JSON for use in a game engine

🧍 Add entities like players or NPCs

💠 Add pathfinding or interaction logic

📦 Dependencies
next

react

simplex-noise

📝 License
MIT — feel free to use, modify, and share.

👤 Author
Made by [Your Name]
Inspired by pixel-art, procedural world building, and retro games.


---

Let me know if you'd like this README to include screenshots, seed-based generat