import { SavedSetting } from '@/types/terrain'
import { STORAGE_KEY } from '@/constants/terrain'

// Load saved settings from localStorage
export function loadSavedSettings(): SavedSetting[] {
  if (typeof window === 'undefined') return []
  
  try {
    const saved = localStorage.getItem(STORAGE_KEY)
    return saved ? JSON.parse(saved) : []
  } catch (error) {
    console.error('Failed to load saved settings:', error)
    return []
  }
}

// Save settings to localStorage
export function saveSetting(setting: SavedSetting): void {
  if (typeof window === 'undefined') return
  
  try {
    const existing = loadSavedSettings()
    const updated = [...existing, setting]
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updated))
  } catch (error) {
    console.error('Failed to save setting:', error)
  }
}

// Delete a setting from localStorage
export function deleteSetting(id: string): void {
  if (typeof window === 'undefined') return
  
  try {
    const existing = loadSavedSettings()
    const updated = existing.filter(setting => setting.id !== id)
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updated))
  } catch (error) {
    console.error('Failed to delete setting:', error)
  }
}
