import { useState, useEffect } from 'react'
import { TerrainCoverage, TerrainPreset, PlanetType } from '@/types/terrain'

interface TerrainCoverageControlsProps {
  coverage: TerrainCoverage
  planetType: PlanetType
  onCoverageChange: (coverage: TerrainCoverage) => void
  onGenerateNew: () => void
  presets: Record<string, TerrainPreset>
}

export function TerrainCoverageControls({ 
  coverage, 
  planetType,
  onCoverageChange, 
  onGenerateNew,
  presets 
}: TerrainCoverageControlsProps) {
  const [localCoverage, setLocalCoverage] = useState<TerrainCoverage>(coverage)

  // Update local state when props change
  useEffect(() => {
    setLocalCoverage(coverage)
  }, [coverage])

  // Calculate remaining land percentages
  const landCoverage = 100 - localCoverage.oceanCoverage
  const remainingLand = 100 - localCoverage.plainsCoverage - localCoverage.hillsCoverage

  const handleOceanChange = (value: number) => {
    const newCoverage = { ...localCoverage, oceanCoverage: value }
    setLocalCoverage(newCoverage)
    onCoverageChange(newCoverage)
  }

  const handlePlainsChange = (value: number) => {
    const maxPlains = 100 - localCoverage.hillsCoverage
    const clampedValue = Math.min(value, maxPlains)
    const newCoverage = { 
      ...localCoverage, 
      plainsCoverage: clampedValue,
      mountainsCoverage: 100 - clampedValue - localCoverage.hillsCoverage
    }
    setLocalCoverage(newCoverage)
    onCoverageChange(newCoverage)
  }

  const handleHillsChange = (value: number) => {
    const maxHills = 100 - localCoverage.plainsCoverage
    const clampedValue = Math.min(value, maxHills)
    const newCoverage = { 
      ...localCoverage, 
      hillsCoverage: clampedValue,
      mountainsCoverage: 100 - localCoverage.plainsCoverage - clampedValue
    }
    setLocalCoverage(newCoverage)
    onCoverageChange(newCoverage)
  }

  const handlePresetSelect = (preset: TerrainPreset) => {
    setLocalCoverage(preset.coverage)
    onCoverageChange(preset.coverage)
  }

  return (
    <div className="mb-6">
      <h2 className="text-lg font-semibold mb-4 text-gray-200">Terrain Coverage</h2>
      
      {/* Quick Presets */}
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-2 text-gray-300">Quick Presets</h3>
        <div className="grid grid-cols-2 gap-2">
          {Object.values(presets).map((preset) => (
            <button
              key={preset.id}
              onClick={() => handlePresetSelect(preset)}
              className="px-3 py-2 bg-gray-700 text-white rounded text-xs hover:bg-gray-600 transition-colors border border-gray-600"
              title={preset.description}
            >
              {preset.name}
            </button>
          ))}
        </div>
      </div>

      {/* Ocean Coverage */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <label className="text-sm font-medium text-gray-300 flex items-center">
            <div 
              className="w-4 h-4 rounded mr-2 border border-gray-500"
              style={{ backgroundColor: planetType.colors.water }}
            />
            Ocean Coverage
          </label>
          <span className="text-sm text-gray-400">{localCoverage.oceanCoverage}%</span>
        </div>
        <input
          type="range"
          min="10"
          max="90"
          value={localCoverage.oceanCoverage}
          onChange={(e) => handleOceanChange(parseInt(e.target.value))}
          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer
                     [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:h-4
                     [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-500
                     [&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:shadow-lg
                     [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:rounded-full
                     [&::-moz-range-thumb]:bg-blue-500 [&::-moz-range-thumb]:cursor-pointer [&::-moz-range-thumb]:border-none"
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>10%</span>
          <span>90%</span>
        </div>
      </div>

      {/* Land Distribution */}
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-3 text-gray-300">
          Land Distribution ({landCoverage.toFixed(0)}% of planet)
        </h3>
        
        {/* Plains */}
        <div className="mb-3">
          <div className="flex justify-between items-center mb-2">
            <label className="text-sm font-medium text-gray-300 flex items-center">
              <div 
                className="w-4 h-4 rounded mr-2 border border-gray-500"
                style={{ backgroundColor: planetType.colors.grass }}
              />
              Plains & Lowlands
            </label>
            <span className="text-sm text-gray-400">{localCoverage.plainsCoverage}%</span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={localCoverage.plainsCoverage}
            onChange={(e) => handlePlainsChange(parseInt(e.target.value))}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer
                       [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:h-4
                       [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-green-500
                       [&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:shadow-lg
                       [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:rounded-full
                       [&::-moz-range-thumb]:bg-green-500 [&::-moz-range-thumb]:cursor-pointer [&::-moz-range-thumb]:border-none"
          />
        </div>

        {/* Hills */}
        <div className="mb-3">
          <div className="flex justify-between items-center mb-2">
            <label className="text-sm font-medium text-gray-300 flex items-center">
              <div 
                className="w-4 h-4 rounded mr-2 border border-gray-500"
                style={{ backgroundColor: planetType.colors.hills }}
              />
              Hills & Forests
            </label>
            <span className="text-sm text-gray-400">{localCoverage.hillsCoverage}%</span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={localCoverage.hillsCoverage}
            onChange={(e) => handleHillsChange(parseInt(e.target.value))}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer
                       [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:h-4
                       [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-orange-500
                       [&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:shadow-lg
                       [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:rounded-full
                       [&::-moz-range-thumb]:bg-orange-500 [&::-moz-range-thumb]:cursor-pointer [&::-moz-range-thumb]:border-none"
          />
        </div>

        {/* Mountains (auto-calculated) */}
        <div className="mb-3">
          <div className="flex justify-between items-center mb-2">
            <label className="text-sm font-medium text-gray-300 flex items-center">
              <div 
                className="w-4 h-4 rounded mr-2 border border-gray-500"
                style={{ backgroundColor: planetType.colors.mountains }}
              />
              Mountains & Peaks
            </label>
            <span className="text-sm text-gray-400">{remainingLand.toFixed(0)}%</span>
          </div>
          <div className="w-full h-2 bg-gray-700 rounded-lg relative">
            <div 
              className="h-full rounded-lg"
              style={{ 
                backgroundColor: planetType.colors.mountains,
                width: `${remainingLand}%`
              }}
            />
          </div>
          <div className="text-xs text-gray-500 mt-1">Auto-calculated</div>
        </div>
      </div>

      {/* Coverage Preview */}
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2 text-gray-300">Coverage Preview</h3>
        <div className="w-full h-8 rounded-lg overflow-hidden border border-gray-600 flex">
          <div
            className="h-full transition-all duration-300"
            style={{
              backgroundColor: planetType.colors.water,
              width: `${localCoverage.oceanCoverage}%`
            }}
            title={`Ocean: ${localCoverage.oceanCoverage}%`}
          />
          <div
            className="h-full transition-all duration-300"
            style={{
              backgroundColor: planetType.colors.grass,
              width: `${landCoverage * localCoverage.plainsCoverage / 100}%`
            }}
            title={`Plains: ${localCoverage.plainsCoverage}% of land`}
          />
          <div
            className="h-full transition-all duration-300"
            style={{
              backgroundColor: planetType.colors.hills,
              width: `${landCoverage * localCoverage.hillsCoverage / 100}%`
            }}
            title={`Hills: ${localCoverage.hillsCoverage}% of land`}
          />
          <div
            className="h-full transition-all duration-300"
            style={{
              backgroundColor: planetType.colors.mountains,
              width: `${landCoverage * remainingLand / 100}%`
            }}
            title={`Mountains: ${remainingLand.toFixed(0)}% of land`}
          />
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>Ocean {localCoverage.oceanCoverage}%</span>
          <span>Land {landCoverage.toFixed(0)}%</span>
        </div>
      </div>

      {/* Generate Button */}
      <button
        onClick={onGenerateNew}
        className="w-full px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors text-sm font-medium"
      >
        Generate New Planet
      </button>
    </div>
  )
}
