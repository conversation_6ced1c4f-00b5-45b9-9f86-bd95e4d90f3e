import { PlanetType } from '@/types/terrain'

interface HelpTextProps {
  currentPlanetType: PlanetType
}

export function HelpText({ currentPlanetType }: HelpTextProps) {
  return (
    <div className="text-xs text-gray-400 space-y-1 border-t border-gray-600 pt-4">
      <p>• Use mouse to orbit around the planet</p>
      <p>• Choose planet type for different themes</p>
      <p>• Adjust weights: + for more, - for less</p>
      <p>• Generate new planet creates different terrain</p>
      <p>• Saved settings preserve exact planet state</p>
      <p className="mt-3 font-medium text-gray-300">Current Planet Colors:</p>
      <div className="flex items-center flex-wrap gap-2 text-xs">
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 rounded border border-gray-500" style={{backgroundColor: currentPlanetType.colors.water}}></div>
          <span>Water</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 rounded border border-gray-500" style={{backgroundColor: currentPlanetType.colors.grass}}></div>
          <span>Grass</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 rounded border border-gray-500" style={{backgroundColor: currentPlanetType.colors.hills}}></div>
          <span>Hills</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 rounded border border-gray-500" style={{backgroundColor: currentPlanetType.colors.mountains}}></div>
          <span>Mountains</span>
        </div>
      </div>
    </div>
  )
}
