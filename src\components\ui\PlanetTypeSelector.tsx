import { PlanetType } from '@/types/terrain'

interface PlanetTypeSelectorProps {
  planetTypes: Record<string, PlanetType>
  currentPlanetType: PlanetType
  onPlanetTypeChange: (planetType: PlanetType) => void
}

export function PlanetTypeSelector({ 
  planetTypes, 
  currentPlanetType, 
  onPlanetTypeChange 
}: PlanetTypeSelectorProps) {
  return (
    <div className="mb-6">
      <h2 className="text-lg font-semibold text-gray-200 mb-3">Planet Type</h2>
      <div className="space-y-2">
        {Object.values(planetTypes).map((planetType) => (
          <button
            key={planetType.id}
            onClick={() => onPlanetTypeChange(planetType)}
            className={`w-full p-3 rounded-lg border text-left transition-colors ${
              currentPlanetType.id === planetType.id
                ? 'border-blue-400 bg-blue-900/30 text-blue-300'
                : 'border-gray-600 bg-gray-700 text-gray-200 hover:bg-gray-600'
            }`}
          >
            <div className="font-medium">{planetType.name}</div>
            <div className="text-xs text-gray-400 mt-1">{planetType.description}</div>
          </button>
        ))}
      </div>
    </div>
  )
}
