{"name": "whisperwood-pact", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "alea": "^1.0.1", "next": "15.4.5", "phaser": "^3.90.0", "react": "19.1.0", "react-dom": "19.1.0", "simplex-noise": "^4.0.3", "three": "^0.179.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.178.1", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}